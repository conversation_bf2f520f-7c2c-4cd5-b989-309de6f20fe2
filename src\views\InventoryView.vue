<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue';
import vehicleStore from '../store/vehicles';

// Access the vehicles and pagination info directly from the store
const vehicles = computed(() => vehicleStore.vehicles.value);
const currentPage = computed(() => vehicleStore.currentPage.value);

// Get store filters (for syncing with local filters)
const storeFilters = computed(() => vehicleStore.activeFilters.value);

// Initialize the store when the component is mounted
onMounted(async () => {
  console.log('InventoryView mounted, initializing store');
  isLoading.value = true; // Ensure loading state is set
  await vehicleStore.initStore();

  // Set up intersection observer after initial load and DOM update
  nextTick(() => {
    console.log('DOM updated after store initialization');

    // Force itemsReady to true after a short delay if we have vehicles
    if (vehicles.value.length > 0 && !itemsReady.value) {
      setTimeout(() => {
        itemsReady.value = true;
        console.log('Forcing itemsReady to true, we have vehicles');

        // Only set up the scroll observer after items are ready and with a longer delay
        // to ensure the DOM is fully rendered and scrolled to initial position
        setTimeout(() => {
          setupScrollObserver();
        }, 1500); // Longer delay to prevent eager loading
      }, 300);
    }
  });
});

// Example vehicle for reference (commented out)
/*
const exampleVehicle = {
  id: 1,
  title: '2023 Porsche 911 GT3',
  price: 229900,
  image: 'https://images.unsplash.com/photo-1614162692292-7ac56d7f7f1e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
  mileage: 1250,
  year: 2023,
  make: 'Porsche',
  model: '911 GT3',
  transmission: 'Automatic',
  fuelType: 'Gasoline',
  bodyType: 'Coupe',
  color: 'Racing Yellow',
  highlights: ['502 HP', '9,000 RPM Redline', 'Track-Ready'],
  description: 'This 2023 Porsche 911 GT3 represents the pinnacle of sports car engineering. With its naturally aspirated 4.0L flat-six engine producing 502 horsepower and capable of revving to 9,000 RPM, it delivers an exhilarating driving experience. The GT3 features track-focused aerodynamics, a sophisticated suspension system, and Porsches renowned PDK transmission for lightning-fast shifts.'
};
*/
// Loading state - keep loading until we have vehicles and they're ready to display
const isLoading = ref(true);
// Watch the store's loading state
watch(() => vehicleStore.isLoading.value, (newValue) => {
  console.log(`Store isLoading changed to: ${newValue}`);
  isLoading.value = newValue;
});

// Track if items are ready to be displayed
const itemsReady = ref(false);

// Track if we're loading more vehicles
const loadingMore = ref(false);

// Watch for vehicles to be loaded and set itemsReady after a small delay
watch(vehicles, (newVehicles) => {
  console.log(`Vehicles changed, now have ${newVehicles.length} vehicles`);
  if (newVehicles.length > 0 && !itemsReady.value) {
    console.log(`Setting itemsReady to true, we have ${newVehicles.length} vehicles`);
    // Keep skeletons visible for a moment to ensure smooth transition
    setTimeout(() => {
      itemsReady.value = true;
      console.log('itemsReady set to true');
    }, 300); // Reduced delay for faster transition
  }
}, { deep: true, immediate: true }); // Added immediate to check initial value

// Watch isLoading but don't reset itemsReady when loading more vehicles
// This allows existing vehicles to remain visible while loading more
watch(() => vehicleStore.isLoading.value, (newIsLoading) => {
  // Only reset itemsReady on initial load, not when loading more pages
  if (newIsLoading && itemsReady.value && currentPage.value === 1) {
    console.log('Store is loading initial page, resetting itemsReady');
    itemsReady.value = false;
  } else if (newIsLoading) {
    console.log('Store is loading more vehicles, keeping existing vehicles visible');
  } else if (!newIsLoading && vehicles.value.length > 0 && !itemsReady.value) {
    // If loading finished and we have vehicles but itemsReady is false, set it to true
    console.log('Store finished loading with vehicles, setting itemsReady to true');
    itemsReady.value = true;
  }
});


// Enhanced scroll-based loading implementation with multiple fallbacks
const loadMoreTrigger = ref(null);
const loadMoreObserver = ref(null);
const scrollDebounceTimer = ref(null);
const loadMoreAttempts = ref(0);
const loadMoreError = ref(false);
const manualLoadTriggered = ref(false);
const lastScrollPosition = ref(0);
const scrollThreshold = ref(1500); // Distance from bottom to trigger load (in px) - significantly increased for much earlier loading

// Function to load more vehicles - with improved state handling and retry logic
const loadMoreVehicles = async (source = 'observer') => {
  // Skip if already loading or no more vehicles
  if (loadingMore.value || !vehicleStore.hasMoreVehicles.value || vehicleStore.isLoading.value) {
    console.log(`[LoadMore] Skipping load - already loading or no more vehicles (source: ${source})`);
    return;
  }

  console.log(`[LoadMore] Starting to load more vehicles (source: ${source})`);
  loadingMore.value = true;
  loadMoreError.value = false; // Reset error state
  manualLoadTriggered.value = false; // Reset manual trigger state

  try {
    // Make sure itemsReady stays true so existing vehicles remain visible
    if (!itemsReady.value && vehicles.value.length > 0) {
      console.log('[LoadMore] Setting itemsReady to true to keep existing vehicles visible');
      itemsReady.value = true;
    }

    // Load the next page of vehicles
    await vehicleStore.loadNextPage();
    console.log('[LoadMore] Successfully loaded next page');

    // Reset attempts counter on success
    loadMoreAttempts.value = 0;

    // Re-observe the trigger after loading with a delay to prevent immediate re-triggering
    setTimeout(() => {
      nextTick(() => {
        console.log('[LoadMore] Re-setting up scroll observer after delay');
        setupScrollObserver();
        // Also reset the scroll handler to ensure it's working
        setupScrollHandler();
      });
    }, 500); // Reduced delay before re-observing for faster response
  } catch (error) {
    console.error('[LoadMore] Error loading more vehicles:', error);
    
    // Check if this is a 416 Range Not Satisfiable error
    const is416Error = error.message && (
      error.message.includes('416') ||
      error.message.includes('Requested range not satisfiable') ||
      (error.code === '416')
    );
    
    if (is416Error) {
      console.log('[LoadMore] Received 416 error - no more vehicles available for current filters');
      // Don't show error UI for this expected condition
      loadMoreError.value = false;
      
      // Update UI to show no more results
      vehicleStore.hasMoreVehicles.value = false;
      vehicleStore.lastPageLoaded.value = true;
      
      // Reset the observer to prevent further attempts
      setupScrollObserver();
      return;
    }
    
    // For other errors, show error UI and attempt recovery
    loadMoreError.value = true;
    loadMoreAttempts.value += 1;

    // If we've had multiple failures, try to recover by resetting the store's pagination state
    if (loadMoreAttempts.value >= 3) {
      console.log('[LoadMore] Multiple failures detected, attempting recovery...');
      try {
        // Force a small delay before recovery attempt
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Try to reset the store's pagination state
        if (vehicleStore.resetPaginationState) {
          await vehicleStore.resetPaginationState();
          console.log('[LoadMore] Store pagination state reset successfully');
        }

        // Reset attempts counter
        loadMoreAttempts.value = 0;
      } catch (recoveryError) {
        console.error('[LoadMore] Recovery attempt failed:', recoveryError);
      }
    }
  } finally {
    // Use a timeout to ensure the loading state is visible for a moment
    // Use a shorter timeout to reset loading state
    setTimeout(() => {
      loadingMore.value = false;
      console.log('[LoadMore] Reset loadingMore to false');
      
      // Preload next page if we have more vehicles and we're not at the end
      if (vehicleStore.hasMoreVehicles.value && !vehicleStore.lastPageLoaded.value) {
        console.log('[LoadMore] Preloading next page for seamless experience');
        // Use a delay before preloading to ensure current page is fully processed
        setTimeout(() => {
          if (!loadingMore.value && vehicleStore.hasMoreVehicles.value) {
            loadMoreVehicles('preload');
          }
        }, 2000); // 2 second delay before preloading next page
      }
    }, 300);
  }
};

// Manual retry function for user-initiated retries
const retryLoadMore = () => {
  console.log('[LoadMore] Manual retry triggered by user');
  manualLoadTriggered.value = true;
  loadMoreVehicles('manual-retry');
};

// Set up the intersection observer with improved reliability
const setupScrollObserver = () => {
  // Skip setup if we're already loading or there are no more vehicles
  if (loadingMore.value || !vehicleStore.hasMoreVehicles.value) {
    console.log('[Observer] Skipping observer setup - already loading or no more vehicles');
    return;
  }

  // Clean up any existing observer
  if (loadMoreObserver.value) {
    console.log('[Observer] Disconnecting existing observer');
    loadMoreObserver.value.disconnect();
    loadMoreObserver.value = null;
  }

  // Clear any existing timer
  if (scrollDebounceTimer.value) {
    clearTimeout(scrollDebounceTimer.value);
    scrollDebounceTimer.value = null;
  }

  // Create new observer with debounce to prevent multiple rapid triggers
  loadMoreObserver.value = new IntersectionObserver((entries) => {
    const entry = entries[0];
    if (entry.isIntersecting && !loadingMore.value && vehicleStore.hasMoreVehicles.value) {
      // Clear any existing timer
      if (scrollDebounceTimer.value) {
        clearTimeout(scrollDebounceTimer.value);
      }

      // Set a new timer to debounce the load
      scrollDebounceTimer.value = setTimeout(() => {
        console.log('[Observer] Trigger element intersecting, loading more vehicles...');
        loadMoreVehicles('intersection-observer');
      }, 100); // Further reduced debounce time for even faster loading
    }
  }, {
    root: null,
    rootMargin: '1500px', // Dramatically increased to start loading extremely early
    threshold: 0.001 // Minimized threshold to trigger with almost any visibility
  });

  // Start observing the trigger element
  if (loadMoreTrigger.value) {
    console.log('[Observer] Starting to observe load more trigger element');
    loadMoreObserver.value.observe(loadMoreTrigger.value);
  } else {
    console.warn('[Observer] Load more trigger element not found, will rely on scroll handler');
    // If the trigger element isn't found, we'll rely on the scroll handler as a fallback
  }
};

// Set up a scroll event handler as a fallback for the intersection observer
const setupScrollHandler = () => {
  // We'll keep the existing scroll handler for other UI elements
  // But add logic to detect when we're near the bottom of the page

  const handleScrollForLoading = () => {
    // Skip if already loading or no more vehicles
    if (loadingMore.value || !vehicleStore.hasMoreVehicles.value || vehicleStore.isLoading.value) {
      return;
    }

    // Calculate distance from bottom of the page
    const scrollPosition = window.scrollY;
    const windowHeight = window.innerHeight;
    const documentHeight = document.documentElement.scrollHeight;
    const distanceFromBottom = documentHeight - (scrollPosition + windowHeight);

    // Store last scroll position to detect direction
    const scrollingDown = scrollPosition > lastScrollPosition.value;
    lastScrollPosition.value = scrollPosition;

    // Only trigger when scrolling down and near the bottom
    if (scrollingDown && distanceFromBottom < scrollThreshold.value) {
      // Clear any existing timer
      if (scrollDebounceTimer.value) {
        clearTimeout(scrollDebounceTimer.value);
      }

      // Set a new timer to debounce the load
      scrollDebounceTimer.value = setTimeout(() => {
        console.log('[ScrollHandler] Near bottom of page, loading more vehicles...');
        loadMoreVehicles('scroll-handler');
      }, 100); // Reduced debounce time for faster loading
    }
  };

  // Add the scroll handler
  window.addEventListener('scroll', handleScrollForLoading, { passive: true });

  // Store the handler for cleanup
  return () => {
    window.removeEventListener('scroll', handleScrollForLoading);
  };
};

// Enhanced lazy loading for images with improved stability - FIXED DOUBLE ANIMATION
const setupLazyLoading = () => {
  // Skip if we're still loading or items aren't ready
  if (isLoading.value || !itemsReady.value) {
    console.log('[LazyLoading] Skipping setup - still loading or items not ready');
    return;
  }

  // Use a shorter delay to ensure DOM is updated but not too long
  setTimeout(() => {
    console.log('[LazyLoading] Setting up lazy loading for images');

    // Use a more specific selector to find actual vehicle cards (not skeletons)
    const vehicleCards = document.querySelectorAll('.card-custom.fade-in-card');
    console.log(`[LazyLoading] Found ${vehicleCards.length} vehicle cards`);

    if (vehicleCards.length === 0) {
      console.log('[LazyLoading] No vehicle cards found, will try again in 300ms');
      // Try again with a shorter delay if no cards found
      setTimeout(() => setupLazyLoading(), 300);
      return;
    }

    if ('loading' in HTMLImageElement.prototype) {
      // Browser supports native lazy loading
      console.log('[LazyLoading] Browser supports native lazy loading');

      // Process images in batches to prevent overwhelming the browser
      // Only select images that don't already have the 'loaded' class to prevent double animation
      const lazyImages = Array.from(document.querySelectorAll('img[loading="lazy"]:not(.loaded)'));
      console.log(`[LazyLoading] Found ${lazyImages.length} lazy-loadable images that aren't already loaded`);

      if (lazyImages.length > 0) {
        // Sort images by their vertical position to prioritize loading top to bottom
        lazyImages.sort((a, b) => {
          const rectA = a.getBoundingClientRect();
          const rectB = b.getBoundingClientRect();
          return rectA.top - rectB.top;
        });

        // Pre-mark images that are already loaded (have a src that's not the placeholder)
        lazyImages.forEach(img => {
          // If the image is already loaded (has a valid src and is not the placeholder)
          if (img.complete && img.naturalWidth > 0 && !img.src.includes('no-image-available.jpg')) {
            // Mark it as loaded immediately to prevent animation
            img.classList.add('loaded');
            console.log('[LazyLoading] Image already loaded, marking as loaded:', img.src);
          }
        });

        // Process images in batches of 5 with a small delay between batches
        const batchSize = 5;
        const processBatch = (startIndex) => {
          const endIndex = Math.min(startIndex + batchSize, lazyImages.length);
          const batch = lazyImages.slice(startIndex, endIndex);

          // Create an observer for this batch
          const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
              if (entry.isIntersecting) {
                const img = entry.target;

                // Skip if already loaded
                if (img.classList.contains('loaded')) {
                  imageObserver.unobserve(img);
                  return;
                }

                // Set fetchpriority to high when it's about to be visible
                img.setAttribute('fetchpriority', 'high');

                // Add loaded class for fade-in effect only if not already loaded
                if (!img.classList.contains('loaded')) {
                  img.classList.add('loaded');
                }

                imageObserver.unobserve(img);
              }
            });
          }, {
            rootMargin: '1500px', // Dramatically increased margin to start loading extremely early
            threshold: 0
          });

          // Observe all images in this batch
          batch.forEach(img => {
            // Skip already loaded images
            if (!img.classList.contains('loaded')) {
              imageObserver.observe(img);
            }
          });

          // Process next batch if there are more images
          if (endIndex < lazyImages.length) {
            setTimeout(() => {
              processBatch(endIndex);
            }, 100); // Small delay between batches
          }
        };

        // Start processing the first batch
        processBatch(0);
      }
    } else {
      // Fallback for browsers that don't support native lazy loading
      console.log('[LazyLoading] Browser does not support native lazy loading, using IntersectionObserver fallback');

      // Find all images with data-src attribute that aren't already loaded
      const lazyImages = Array.from(document.querySelectorAll('img[data-src]:not(.loaded)'));
      console.log(`[LazyLoading] Found ${lazyImages.length} images with data-src that aren't already loaded`);

      if (lazyImages.length > 0) {
        // Sort images by their vertical position
        lazyImages.sort((a, b) => {
          const rectA = a.getBoundingClientRect();
          const rectB = b.getBoundingClientRect();
          return rectA.top - rectB.top;
        });

        // Process images in batches
        const batchSize = 5;
        const processBatch = (startIndex) => {
          const endIndex = Math.min(startIndex + batchSize, lazyImages.length);
          const batch = lazyImages.slice(startIndex, endIndex);

          const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
              if (entry.isIntersecting) {
                const img = entry.target;

                // Skip if already loaded
                if (img.classList.contains('loaded')) {
                  imageObserver.unobserve(img);
                  return;
                }

                img.src = img.dataset.src;
                img.removeAttribute('data-src');

                // Add loaded class only if not already loaded
                if (!img.classList.contains('loaded')) {
                  img.classList.add('loaded');
                }

                imageObserver.unobserve(img);
              }
            });
          }, {
            rootMargin: '1500px', // Dramatically increased for extremely early loading
            threshold: 0
          });

          batch.forEach(img => {
            // Skip already loaded images
            if (!img.classList.contains('loaded')) {
              imageObserver.observe(img);
            }
          });

          if (endIndex < lazyImages.length) {
            setTimeout(() => {
              processBatch(endIndex);
            }, 100);
          }
        };

        processBatch(0);
      }
    }
  }, 200); // Reduced delay for faster response
};

// Call setupLazyLoading when vehicles change
watch(vehicles, () => {
  nextTick(() => {
    setupLazyLoading();
  });
});

// Filter states
const filters = ref({
  search: '',
  priceRange: [0, 300000],
  kilometersRange: [0, 500000],
  yearRange: [1900, new Date().getFullYear() + 1], // Wider year range to include all vehicles
  make: [],
  model: [],
  color: [],
  bodyType: [],
  transmission: [],
  fuelType: []
});

// Store initial filter values for reset
const initialFilters = JSON.parse(JSON.stringify(filters.value));

// Track if make is selected to enable model filter
const isMakeSelected = computed(() => filters.value.make.length > 0);

// Sort state
const sortOption = ref('newest');

// Watch for sort option changes
watch(sortOption, async (newSortOption) => {
  console.log('Sort option changed to:', newSortOption);
  // Update the store sort option
  await vehicleStore.updateSortOption(newSortOption);
});

// Available filter options (derived from vehicles data)
const makeOptions = computed(() => {
  const makes = new Set(vehicles.value.map(v => v.make));
  return Array.from(makes);
});

const bodyTypeOptions = computed(() => {
  const types = new Set(vehicles.value.map(v => v.bodyStyle || 'Unknown'));
  return Array.from(types);
});

const transmissionOptions = computed(() => {
  const transmissions = new Set(vehicles.value.map(v => v.transmission || 'Unknown'));
  return Array.from(transmissions);
});

const fuelTypeOptions = computed(() => {
  const fuels = new Set(vehicles.value.map(v => v.fuelType || 'Unknown'));
  return Array.from(fuels);
});

const colorOptions = computed(() => {
  const colors = new Set(vehicles.value.map(v => v.exteriorColor || 'Unknown'));
  return Array.from(colors);
});

const modelOptions = computed(() => {
  if (filters.value.make.length === 0) {
    return [];
  }

  // Get models only for the selected makes
  const models = new Set(
    vehicles.value
      .filter(v => filters.value.make.includes(v.make))
      .map(v => v.model)
  );

  return Array.from(models);
});

// Filtered and sorted vehicles - simplified to rely on server-side filtering
const filteredVehicles = computed(() => {
  console.log(`[Filter] Computing filteredVehicles with ${vehicles.value.length} vehicles from store`);
  
  // Since we're now using immediate server-side filtering for all filter changes,
  // we can simply return the vehicles from the store, which will already be filtered
  // by the server based on the current filters.
  return vehicles.value;
});

// Watch filteredVehicles to setup lazy loading when they change
watch(() => filteredVehicles.value.length, (newLength, oldLength) => {
  if (newLength > 0 && itemsReady.value) {
    console.log(`[Watcher] filteredVehicles changed from ${oldLength} to ${newLength}, setting up lazy loading`);
    // Wait for the DOM to update
    nextTick(() => {
      // Add a small delay to ensure the DOM is fully updated
      setTimeout(() => {
        setupLazyLoading();
        // Only reset scroll observer when filters change significantly (e.g., fewer results)
        if (oldLength > newLength) {
          console.log('[Watcher] Filter reduced results, resetting scroll observer');
          setupScrollObserver();
        }
      }, 300);
    });
  }
});

// Also watch itemsReady to setup lazy loading when it becomes true
watch(itemsReady, (newValue) => {
  if (newValue && filteredVehicles.value.length > 0) {
    console.log('[Watcher] Items are ready, setting up lazy loading');
    // Wait for the DOM to update
    nextTick(() => {
      // Add a small delay to ensure the DOM is fully updated
      setTimeout(() => {
        setupLazyLoading();
        // We don't need to call setupScrollObserver here as it's already called
        // in the onMounted hook with a proper delay
      }, 300);
    });
  }
});

// Toggle filter for array-based filters
const toggleFilter = async (filterType, value) => {
  console.log(`[Filter] Toggling ${filterType} filter with value: ${value}`);
  
  // Create a copy of the current filters to avoid reactivity issues
  const updatedFilters = JSON.parse(JSON.stringify(filters.value));
  
  const index = updatedFilters[filterType].indexOf(value);
  if (index === -1) {
    console.log(`[Filter] Adding ${value} to ${filterType} filters`);
    updatedFilters[filterType].push(value);
  } else {
    console.log(`[Filter] Removing ${value} from ${filterType} filters`);
    updatedFilters[filterType].splice(index, 1);
  }
  
  // Update the local filters first
  filters.value = updatedFilters;

  // Reset loading state
  isLoading.value = true;
  itemsReady.value = false;
  
  try {
    // Apply filters immediately instead of debouncing for better UX
    console.log(`[Filter] Applying updated filters to store:`, updatedFilters);
    await vehicleStore.updateFilters({
      ...updatedFilters,
      sortOption: sortOption.value
    });
    
    console.log(`[Filter] Filters applied successfully`);
    
    // Reset scroll position to top when filters change
    window.scrollTo({ top: 0, behavior: 'smooth' });
  } catch (error) {
    console.error(`[Filter] Error applying filters:`, error);
    // Ensure loading state is reset if there's an error
    isLoading.value = false;
    itemsReady.value = true;
  }
};

// Debounce timer for filter updates
let filterUpdateTimer = null;

// Debounced function to update filters in the store
const debouncedUpdateFilters = () => {
  // Clear any existing timer
  if (filterUpdateTimer) {
    clearTimeout(filterUpdateTimer);
  }

  // Set a new timer
  filterUpdateTimer = setTimeout(async () => {
    console.log('[Filter] Updating store filters after debounce');
    
    // Reset loading state
    isLoading.value = true;
    
    try {
      await vehicleStore.updateFilters({
        ...filters.value,
        sortOption: sortOption.value
      });
      console.log('[Filter] Debounced filter update applied successfully');
    } catch (error) {
      console.error('[Filter] Error applying debounced filter update:', error);
      // Ensure loading state is reset if there's an error
      isLoading.value = false;
    }
  }, 500); // 500ms debounce
};

// Reset all filters
const resetFilters = async () => {
  console.log('[Filter] Resetting all filters to initial state');
  
  // Reset local filters - create a fresh copy to avoid reference issues
  filters.value = JSON.parse(JSON.stringify(initialFilters));
  
  // Reset sort option to default first
  sortOption.value = 'newest';
  
  // Reset loading state
  isLoading.value = true;
  itemsReady.value = false;
  
  try {
    console.log('[Filter] Applying reset filters to store');
    // Apply reset only once with the updated sort option
    await vehicleStore.updateFilters({
      ...filters.value,
      sortOption: 'newest'
    });
    
    console.log('[Filter] Filters reset successfully');
    
    // Reset scroll position to top
    window.scrollTo({ top: 0, behavior: 'smooth' });
  } catch (error) {
    console.error('[Filter] Error resetting filters:', error);
    // Ensure loading state is reset if there's an error
    isLoading.value = false;
    itemsReady.value = true;
  }
};

// Format price
const formatPrice = (price) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'CAD',
    maximumFractionDigits: 0
  }).format(price);
};

// Format large numbers (used in the template for mileage display)
const formatNumber = (num) => {
  return new Intl.NumberFormat('en-US').format(num);
};

// Mobile filter visibility
const showMobileFilters = ref(false);

const toggleMobileFilters = () => {
  showMobileFilters.value = !showMobileFilters.value;
  if (showMobileFilters.value) {
    document.body.classList.add('overflow-hidden');
  } else {
    document.body.classList.remove('overflow-hidden');
  }
};

// Filter category expansion and popover state
const expandedCategories = ref({
  priceRange: true,
  kilometersRange: false,
  yearRange: false,
  make: false,
  model: false,
  color: false,
  bodyType: false,
  transmission: false,
  fuelType: false
});

const activePopover = ref(null);

const toggleCategory = (category) => {
  expandedCategories.value[category] = !expandedCategories.value[category];

  // If expanding, scroll to the category after a short delay to allow for rendering
  if (expandedCategories.value[category]) {
    setTimeout(() => {
      const element = document.getElementById(`mobile-category-${category}`);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
      }
    }, 100);
  }
};

const togglePopover = (category) => {
  if (activePopover.value === category) {
    activePopover.value = null;
  } else {
    // Close any open popover first
    activePopover.value = null;

    // Use nextTick to ensure the previous popover is closed before opening the new one
    nextTick(() => {
      activePopover.value = category;

      // Wait for the popover to render, then scroll to it
      setTimeout(() => {
        if (filterRefs.value[category]) {
          filterRefs.value[category].scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
      }, 100);
    });
  }
};

const closePopover = () => {
  activePopover.value = null;
};

// Click outside to close popover and scroll tracking
const filterRefs = ref({});
const isScrolled = ref(false);

const handleScroll = () => {
  isScrolled.value = window.scrollY > 50;
};

const handleClickOutside = (e) => {
  if (activePopover.value &&
      filterRefs.value[activePopover.value] &&
      !filterRefs.value[activePopover.value].contains(e.target)) {
    closePopover();
  }
};

// Range slider helpers
const ensurePriceRangeOrder = async () => {
  console.log('[Filter] Ensuring price range order');
  if (filters.value.priceRange[0] > filters.value.priceRange[1]) {
    filters.value.priceRange[0] = filters.value.priceRange[1];
  }
  
  // Reset loading state
  isLoading.value = true;
  itemsReady.value = false;
  
  try {
    // Apply filters immediately for better UX
    console.log('[Filter] Applying price range filter update');
    await vehicleStore.updateFilters({
      ...filters.value,
      sortOption: sortOption.value
    });
    console.log('[Filter] Price range filter applied successfully');
  } catch (error) {
    console.error('[Filter] Error applying price range filter:', error);
    isLoading.value = false;
    itemsReady.value = true;
  }
};

const ensureKilometersRangeOrder = async () => {
  console.log('[Filter] Ensuring kilometers range order');
  if (filters.value.kilometersRange[0] > filters.value.kilometersRange[1]) {
    filters.value.kilometersRange[0] = filters.value.kilometersRange[1];
  }
  
  // Reset loading state
  isLoading.value = true;
  itemsReady.value = false;
  
  try {
    // Apply filters immediately for better UX
    console.log('[Filter] Applying kilometers range filter update');
    await vehicleStore.updateFilters({
      ...filters.value,
      sortOption: sortOption.value
    });
    console.log('[Filter] Kilometers range filter applied successfully');
  } catch (error) {
    console.error('[Filter] Error applying kilometers range filter:', error);
    isLoading.value = false;
    itemsReady.value = true;
  }
};

const ensureYearRangeOrder = async () => {
  console.log('[Filter] Ensuring year range order');
  if (filters.value.yearRange[0] > filters.value.yearRange[1]) {
    filters.value.yearRange[0] = filters.value.yearRange[1];
  }
  
  // Reset loading state
  isLoading.value = true;
  itemsReady.value = false;
  
  try {
    // Apply filters immediately for better UX
    console.log('[Filter] Applying year range filter update');
    await vehicleStore.updateFilters({
      ...filters.value,
      sortOption: sortOption.value
    });
    console.log('[Filter] Year range filter applied successfully');
  } catch (error) {
    console.error('[Filter] Error applying year range filter:', error);
    isLoading.value = false;
    itemsReady.value = true;
  }
};

// Watch for search input changes
watch(() => filters.value.search, async (newValue) => {
  console.log(`[Filter] Search input changed to: "${newValue}"`);
  
  // Reset loading state
  isLoading.value = true;
  itemsReady.value = false;
  
  try {
    // Apply filters immediately for better UX
    console.log('[Filter] Applying search filter update');
    await vehicleStore.updateFilters({
      ...filters.value,
      sortOption: sortOption.value
    });
    console.log('[Filter] Search filter applied successfully');
  } catch (error) {
    console.error('[Filter] Error applying search filter:', error);
    isLoading.value = false;
    itemsReady.value = true;
  }
});

// Calculate percentage for slider track
const getTrackPercentage = (value, min, max) => {
  return ((value - min) / (max - min)) * 100;
};

// Filter state tracking for animations
const hasActiveFilters = computed(() => {
  return (
    filters.value.search !== '' ||
    filters.value.priceRange[0] !== initialFilters.priceRange[0] ||
    filters.value.priceRange[1] !== initialFilters.priceRange[1] ||
    filters.value.kilometersRange[0] !== initialFilters.kilometersRange[0] ||
    filters.value.kilometersRange[1] !== initialFilters.kilometersRange[1] ||
    filters.value.yearRange[0] !== initialFilters.yearRange[0] ||
    filters.value.yearRange[1] !== initialFilters.yearRange[1] ||
    filters.value.make.length > 0 ||
    filters.value.model.length > 0 ||
    filters.value.color.length > 0 ||
    filters.value.bodyType.length > 0 ||
    filters.value.transmission.length > 0 ||
    filters.value.fuelType.length > 0
  );
});

// Count of active filters (for badge)
const activeFilterCount = computed(() => {
  let count = 0;

  if (filters.value.search !== '') count++;
  if (filters.value.priceRange[0] !== initialFilters.priceRange[0] ||
      filters.value.priceRange[1] !== initialFilters.priceRange[1]) count++;
  if (filters.value.kilometersRange[0] !== initialFilters.kilometersRange[0] ||
      filters.value.kilometersRange[1] !== initialFilters.kilometersRange[1]) count++;
  if (filters.value.yearRange[0] !== initialFilters.yearRange[0] ||
      filters.value.yearRange[1] !== initialFilters.yearRange[1]) count++;

  count += filters.value.make.length;
  count += filters.value.model.length;
  count += filters.value.color.length;
  count += filters.value.bodyType.length;
  count += filters.value.transmission.length;
  count += filters.value.fuelType.length;

  return count;
});

// Store cleanup functions
const cleanupFunctions = ref([]);

onMounted(() => {
  window.addEventListener('scroll', handleScroll);
  window.addEventListener('click', handleClickOutside);
  handleScroll(); // Check initial scroll position

  // Sync local filters with store filters
  if (storeFilters.value) {
    // Only update if there are actual filters in the store
    filters.value = { ...storeFilters.value };
    sortOption.value = storeFilters.value.sortOption || 'newest';
  } else {
    // Initialize store filters with local filters
    vehicleStore.updateFilters({
      ...filters.value,
      sortOption: sortOption.value
    });
  }

  // Set up the scroll handler as a fallback mechanism
  const scrollHandlerCleanup = setupScrollHandler();
  if (scrollHandlerCleanup) {
    cleanupFunctions.value.push(scrollHandlerCleanup);
  }

  // Initialize last scroll position
  lastScrollPosition.value = window.scrollY;

  // Only setup lazy loading after initial render
  // The scroll observer will be set up after items are ready with a delay
  nextTick(() => {
    setupLazyLoading();
    // We don't call setupScrollObserver() here anymore to prevent duplicate setup
  });

  // Set up a periodic check to ensure loading is working
  // This helps recover from edge cases where both the observer and scroll handler might fail
  const periodicCheckInterval = setInterval(() => {
    // Only run the check if:
    // 1. We have more vehicles to load
    // 2. We're not currently loading
    // 3. We're not showing an error
    // 4. The user has scrolled down significantly (at least 1000px)
    if (
      vehicleStore.hasMoreVehicles.value &&
      !loadingMore.value &&
      !loadMoreError.value &&
      window.scrollY > 1000 &&
      filteredVehicles.value.length > 0
    ) {
      const windowHeight = window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight;
      const scrollPosition = window.scrollY;
      const distanceFromBottom = documentHeight - (scrollPosition + windowHeight);

      // If we're anywhere near the bottom but not loading, something might be wrong
      if (distanceFromBottom < 3000) {
        console.log('[PeriodicCheck] Near bottom but not loading, triggering load as fallback');
        loadMoreVehicles('periodic-check');
      }
    }
  }, 5000); // Check every 5 seconds

  // Store the interval for cleanup
  cleanupFunctions.value.push(() => clearInterval(periodicCheckInterval));
});

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll);
  window.removeEventListener('click', handleClickOutside);
  document.body.classList.remove('overflow-hidden');

  // Clean up observers
  if (loadMoreObserver.value) {
    loadMoreObserver.value.disconnect();
  }

  // Clear any pending timers
  if (scrollDebounceTimer.value) {
    clearTimeout(scrollDebounceTimer.value);
  }

  // Run all cleanup functions
  cleanupFunctions.value.forEach(cleanup => {
    if (typeof cleanup === 'function') {
      cleanup();
    }
  });
});

// Helper function to attempt extracting trim/details from the title
// REVISED for more robustness
const extractDetails = (vehicle) => {
  // Default values in case vehicle data is incomplete
  const defaultReturn = { mainTitle: 'Vehicle Details Unavailable', subtitle: '' };

  // Ensure vehicle object exists
  if (!vehicle) {
    console.warn("extractDetails called with null or undefined vehicle");
    return defaultReturn;
  }

  // Safely access properties using optional chaining and nullish coalescing
  const title = vehicle.title ?? '';
  const year = vehicle.year; // Assuming year is generally reliable, but check if it can be null/undefined
  const make = vehicle.make ?? '';
  const model = vehicle.model ?? '';

  // Construct main title - Check if core components exist
  let mainTitle = defaultReturn.mainTitle;
  if (year && make && model) {
    mainTitle = `${year} ${make} ${model}`;
  } else if (title) {
    // Fallback to using the original title if make/model/year are missing
    mainTitle = title;
  }
  // If even title is missing, mainTitle remains 'Vehicle Details Unavailable'

  // Attempt to extract subtitle only if we have a title and the main components
  let subtitle = '';
  if (title && year && make && model) {
    try {
      const yearStr = String(year);
      // Be careful with replace - use regex for word boundaries if needed, or simple replace
      let tempSubtitle = title;

      // Replace make and model first (potentially longer strings)
      // Using replace directly might be okay if make/model don't appear elsewhere
      // A more robust way might involve finding the first instance after the year
      tempSubtitle = tempSubtitle.replace(make, '');
      tempSubtitle = tempSubtitle.replace(model, '');
      // Replace year last
      tempSubtitle = tempSubtitle.replace(yearStr, '');

      // Cleanup
      subtitle = tempSubtitle.trim().replace(/\s+/g, ' ').replace(/^[-|/\s]+|[-|/\s]+$/g, '');

    } catch (error) {
      console.error("Error extracting subtitle for vehicle:", vehicle.id, error);
      subtitle = ''; // Reset subtitle on error
    }
  }

  return {
    mainTitle: mainTitle,
    subtitle: subtitle
  };
};
</script>

<template>

  <!-- Inventory Section -->
  <section class="section bg-light">
    <div class="w-full px-4">
      <div class="flex flex-col lg:flex-row gap-8">
        <!-- Filters - Desktop -->
        <div class="hidden lg:block w-72 flex-shrink-0">
          <div class="bg-white rounded-lg shadow-custom p-6 sticky top-24 max-h-[calc(100vh-120px)] overflow-y-auto transition-all duration-300 relative">
            <!-- Overlay for fading other content when popover is active -->
            <div v-if="activePopover" class="absolute inset-0 bg-white/70 backdrop-blur-sm z-20 pointer-events-none transition-opacity duration-300"></div>

            <div class="mb-6 flex justify-between items-center relative z-20">
              <h3 class="text-lg font-heading font-bold">Filters</h3>
              <button @click="resetFilters" class="text-secondary text-sm font-medium hover:underline flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Reset All
              </button>
            </div>

            <!-- Active Filters Summary -->
            <div v-if="hasActiveFilters"
                 class="mb-6 flex flex-wrap gap-2 relative z-20">
              <div v-for="make in filters.make" :key="`active-make-${make}`"
                   class="bg-gray-100 text-gray-700 text-xs px-3 py-1 rounded-full flex items-center">
                {{ make }}
                <button @click="toggleFilter('make', make)" class="ml-1 text-gray-500 hover:text-gray-700">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <div v-for="model in filters.model" :key="`active-model-${model}`"
                   class="bg-gray-100 text-gray-700 text-xs px-3 py-1 rounded-full flex items-center">
                {{ model }}
                <button @click="toggleFilter('model', model)" class="ml-1 text-gray-500 hover:text-gray-700">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <div v-for="color in filters.color" :key="`active-color-${color}`"
                   class="bg-gray-100 text-gray-700 text-xs px-3 py-1 rounded-full flex items-center">
                {{ color }}
                <button @click="toggleFilter('color', color)" class="ml-1 text-gray-500 hover:text-gray-700">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <!-- Other active filter pills can be shown here -->
            </div>

            <!-- Price Range Filter -->
            <div class="mb-6 relative">
              <!-- TEMPORARILY DISABLED: Price Range Filter -->
              <button
                class="w-full flex items-center justify-between bg-gray-50 transition-colors rounded-lg p-3 text-left relative disabled-filter"
                ref="filterRefs['priceRange']"
                disabled
              >
                <!-- Coming soon label -->
                <div class="absolute right-3 top-0 transform -translate-y-1/2">
                  <span class="text-xs font-medium text-red-500 bg-white px-1 rounded">Coming soon!</span>
                </div>
                <div class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <div>
                    <span class="font-medium">Price Range</span>
                    <p class="text-xs text-gray-500 mt-0.5">{{ formatPrice(filters.priceRange[0]) }} - {{ formatPrice(filters.priceRange[1]) }}</p>
                  </div>
                </div>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 transition-transform" :class="{ 'rotate-180': activePopover === 'priceRange' }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              <!-- Price Range Popover -->
              <div v-if="activePopover === 'priceRange'" class="absolute z-30 left-0 right-0 mt-2 bg-white rounded-lg shadow-lg p-4 border border-gray-200 transition-all duration-300 max-h-[300px] overflow-y-auto">
                <div class="flex items-center justify-between mb-2">
                  <span class="text-sm font-medium text-gray-600">{{ formatPrice(filters.priceRange[0]) }}</span>
                  <span class="text-sm font-medium text-gray-600">{{ formatPrice(filters.priceRange[1]) }}</span>
                </div>
                <!-- Modern Range Slider -->
                <div class="relative h-14 pt-5 pb-2 range-slider-container">
                  <!-- Track background - positioned below the slider controls -->
                  <div class="absolute top-6 left-0 right-0 h-2 bg-gray-200 rounded-full" style="z-index: 5;"></div>

                  <!-- Selected range - positioned above track but below thumbs -->
                  <div
                    class="absolute top-6 h-2 bg-gradient-to-r from-secondary/70 to-secondary rounded-full"
                    :style="{
                      left: `${getTrackPercentage(filters.priceRange[0], 0, 300000)}%`,
                      right: `${100 - getTrackPercentage(filters.priceRange[1], 0, 300000)}%`,
                      zIndex: 6
                    }"
                  ></div>

                  <!-- Slider controls - positioned at the top layer -->
                  <div class="relative mt-[-10px]" style="z-index: 15;">
                    <input
                      type="range"
                      v-model.number="filters.priceRange[0]"
                      min="0"
                      max="300000"
                      step="10000"
                      class="range-slider range-slider-min"
                      @input="ensurePriceRangeOrder"
                      @focus="$event.target.blur()"
                    />

                    <input
                      type="range"
                      v-model.number="filters.priceRange[1]"
                      min="0"
                      max="300000"
                      step="10000"
                      class="range-slider range-slider-max"
                      @input="ensurePriceRangeOrder"
                      @focus="$event.target.blur()"
                    />
                  </div>
                </div>

                <div class="grid grid-cols-2 gap-4 mt-4">
                  <div>
                    <label class="text-xs text-gray-500 mb-1 block">Min Price</label>
                    <input
                      type="text"
                      v-model="filters.priceRange[0]"
                      class="w-full p-2 border border-gray-300 rounded text-sm"
                      @input="filters.priceRange[0] = Math.min(parseInt(filters.priceRange[0]) || 0, filters.priceRange[1])"
                    />
                  </div>
                  <div>
                    <label class="text-xs text-gray-500 mb-1 block">Max Price</label>
                    <input
                      type="text"
                      v-model="filters.priceRange[1]"
                      class="w-full p-2 border border-gray-300 rounded text-sm"
                      @input="filters.priceRange[1] = Math.max(parseInt(filters.priceRange[1]) || 0, filters.priceRange[0])"
                    />
                  </div>
                </div>

                <!-- Preset Buttons -->
                <div class="flex flex-wrap gap-2 mt-4">
                  <button
                    @click="filters.priceRange = [0, 50000]"
                    class="text-xs px-3 py-1 rounded-full border border-gray-300 hover:bg-gray-100 transition-colors"
                    :class="{'bg-secondary/10 border-secondary/30': filters.priceRange[0] === 0 && filters.priceRange[1] === 50000}"
                  >
                    &lt; $50k
                  </button>
                  <button
                    @click="filters.priceRange = [50000, 100000]"
                    class="text-xs px-3 py-1 rounded-full border border-gray-300 hover:bg-gray-100 transition-colors"
                    :class="{'bg-secondary/10 border-secondary/30': filters.priceRange[0] === 50000 && filters.priceRange[1] === 100000}"
                  >
                    $50k - $100k
                  </button>
                  <button
                    @click="filters.priceRange = [100000, 200000]"
                    class="text-xs px-3 py-1 rounded-full border border-gray-300 hover:bg-gray-100 transition-colors"
                    :class="{'bg-secondary/10 border-secondary/30': filters.priceRange[0] === 100000 && filters.priceRange[1] === 200000}"
                  >
                    $100k - $200k
                  </button>
                  <button
                    @click="filters.priceRange = [200000, 300000]"
                    class="text-xs px-3 py-1 rounded-full border border-gray-300 hover:bg-gray-100 transition-colors"
                    :class="{'bg-secondary/10 border-secondary/30': filters.priceRange[0] === 200000 && filters.priceRange[1] === 300000}"
                  >
                    &gt; $200k
                  </button>
                </div>
              </div>
            </div>

            <!-- Kilometers Range Filter -->
            <div class="mb-6 relative">
              <!-- TEMPORARILY DISABLED: Kilometers Range Filter -->
              <button
                class="w-full flex items-center justify-between bg-gray-50 transition-colors rounded-lg p-3 text-left relative disabled-filter"
                ref="filterRefs['kilometersRange']"
                disabled
              >
                <!-- Coming soon label -->
                <div class="absolute right-3 top-0 transform -translate-y-1/2">
                  <span class="text-xs font-medium text-red-500 bg-white px-1 rounded">Coming soon!</span>
                </div>
                <div class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <div>
                    <span class="font-medium">Kilometers</span>
                    <p class="text-xs text-gray-500 mt-0.5">{{ filters.kilometersRange[0].toLocaleString() }} - {{ filters.kilometersRange[1].toLocaleString() }} km</p>
                  </div>
                </div>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 transition-transform" :class="{ 'rotate-180': activePopover === 'kilometersRange' }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              <!-- Kilometers Range Popover -->
              <div v-if="activePopover === 'kilometersRange'" class="absolute z-30 left-0 right-0 mt-2 bg-white rounded-lg shadow-lg p-4 border border-gray-200 transition-all duration-300 max-h-[300px] overflow-y-auto">
                <div class="flex items-center justify-between mb-2">
                  <span class="text-sm font-medium text-gray-600">{{ filters.kilometersRange[0].toLocaleString() }} km</span>
                  <span class="text-sm font-medium text-gray-600">{{ filters.kilometersRange[1].toLocaleString() }} km</span>
                </div>
                <div class="relative h-14 pt-5 pb-2 range-slider-container">
                  <!-- Track background - positioned below the slider controls -->
                  <div class="absolute top-6 left-0 right-0 h-2 bg-gray-200 rounded-full" style="z-index: 5;"></div>

                  <!-- Selected range - positioned above track but below thumbs -->
                  <div
                    class="absolute top-6 h-2 bg-gradient-to-r from-secondary/70 to-secondary rounded-full"
                    :style="{
                      left: `${getTrackPercentage(filters.kilometersRange[0], 0, 500000)}%`,
                      right: `${100 - getTrackPercentage(filters.kilometersRange[1], 0, 500000)}%`,
                      zIndex: 6
                    }"
                  ></div>

                  <!-- Slider controls - positioned at the top layer -->
                  <div class="relative mt-[-10px]" style="z-index: 15;">
                    <input
                      type="range"
                      v-model.number="filters.kilometersRange[0]"
                      min="0"
                      max="500000"
                      step="5000"
                      class="range-slider range-slider-min"
                      @input="ensureKilometersRangeOrder"
                      @focus="$event.target.blur()"
                    />

                    <input
                      type="range"
                      v-model.number="filters.kilometersRange[1]"
                      min="0"
                      max="500000"
                      step="5000"
                      class="range-slider range-slider-max"
                      @input="ensureKilometersRangeOrder"
                      @focus="$event.target.blur()"
                    />
                  </div>
                </div>
                <div class="grid grid-cols-2 gap-4 mt-4">
                  <div>
                    <label class="text-xs text-gray-500 mb-1 block">Min Kilometers</label>
                    <input
                      type="number"
                      v-model.number="filters.kilometersRange[0]"
                      min="0"
                      max="500000"
                      step="5000"
                      class="w-full p-2 border border-gray-300 rounded text-sm"
                      @input="ensureKilometersRangeOrder"
                    />
                  </div>
                  <div>
                    <label class="text-xs text-gray-500 mb-1 block">Max Kilometers</label>
                    <input
                      type="number"
                      v-model.number="filters.kilometersRange[1]"
                      min="0"
                      max="500000"
                      step="5000"
                      class="w-full p-2 border border-gray-300 rounded text-sm"
                      @input="ensureKilometersRangeOrder"
                    />
                  </div>
                </div>
              </div>
            </div>

            <!-- Year Range Filter -->
            <div class="mb-6 relative">
              <!-- TEMPORARILY DISABLED: Year Range Filter -->
              <button
                class="w-full flex items-center justify-between bg-gray-50 transition-colors rounded-lg p-3 text-left relative disabled-filter"
                ref="filterRefs['yearRange']"
                disabled
              >
                <!-- Coming soon label -->
                <div class="absolute right-3 top-0 transform -translate-y-1/2">
                  <span class="text-xs font-medium text-red-500 bg-white px-1 rounded">Coming soon!</span>
                </div>
                <div class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <div>
                    <span class="font-medium">Year</span>
                    <p class="text-xs text-gray-500 mt-0.5">{{ filters.yearRange[0] }} - {{ filters.yearRange[1] }}</p>
                  </div>
                </div>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 transition-transform" :class="{ 'rotate-180': activePopover === 'yearRange' }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              <!-- Year Range Popover -->
              <div v-if="activePopover === 'yearRange'" class="absolute z-30 left-0 right-0 mt-2 bg-white rounded-lg shadow-lg p-4 border border-gray-200 transition-all duration-300 max-h-[300px] overflow-y-auto">
                <div class="flex items-center justify-between mb-2">
                  <span class="text-sm font-medium text-gray-600">{{ filters.yearRange[0] }}</span>
                  <span class="text-sm font-medium text-gray-600">{{ filters.yearRange[1] }}</span>
                </div>
                <div class="relative h-14 pt-5 pb-2 range-slider-container">
                  <!-- Track background - positioned below the slider controls -->
                  <div class="absolute top-6 left-0 right-0 h-2 bg-gray-200 rounded-full" style="z-index: 5;"></div>

                  <!-- Selected range - positioned above track but below thumbs -->
                  <div
                    class="absolute top-6 h-2 bg-gradient-to-r from-secondary/70 to-secondary rounded-full"
                    :style="{
                      left: `${getTrackPercentage(filters.yearRange[0], 1990, new Date().getFullYear())}%`,
                      right: `${100 - getTrackPercentage(filters.yearRange[1], 1990, new Date().getFullYear())}%`,
                      zIndex: 6
                    }"
                  ></div>

                  <!-- Slider controls - positioned at the top layer -->
                  <div class="relative mt-[-10px]" style="z-index: 15;">
                    <input
                      type="range"
                      v-model.number="filters.yearRange[0]"
                      min="1990"
                      :max="new Date().getFullYear()"
                      step="1"
                      class="range-slider range-slider-min"
                      @input="ensureYearRangeOrder"
                      @focus="$event.target.blur()"
                    />

                    <input
                      type="range"
                      v-model.number="filters.yearRange[1]"
                      min="1990"
                      :max="new Date().getFullYear()"
                      step="1"
                      class="range-slider range-slider-max"
                      @input="ensureYearRangeOrder"
                      @focus="$event.target.blur()"
                    />
                  </div>
                </div>
                <div class="grid grid-cols-2 gap-4 mt-4">
                  <div>
                    <label class="text-xs text-gray-500 mb-1 block">Min Year</label>
                    <input
                      type="number"
                      v-model.number="filters.yearRange[0]"
                      min="2020"
                      max="2024"
                      step="1"
                      class="w-full p-2 border border-gray-300 rounded text-sm"
                      @input="ensureYearRangeOrder"
                    />
                  </div>
                  <div>
                    <label class="text-xs text-gray-500 mb-1 block">Max Year</label>
                    <input
                      type="number"
                      v-model.number="filters.yearRange[1]"
                      min="2020"
                      max="2024"
                      step="1"
                      class="w-full p-2 border border-gray-300 rounded text-sm"
                      @input="ensureYearRangeOrder"
                    />
                  </div>
                </div>
              </div>
            </div>

            <!-- Make Filter -->
            <div class="mb-6 relative">
              <button
                @click="togglePopover('make')"
                class="w-full flex items-center justify-between bg-gray-50 hover:bg-gray-100 transition-colors rounded-lg p-3 text-left"
                :class="{ 'bg-gray-100 ring-2 ring-secondary/20 relative z-30': activePopover === 'make' }"
                ref="filterRefs['make']"
              >
                <div class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                  <div>
                    <span class="font-medium">Make</span>
                    <p v-if="filters.make.length" class="text-xs text-gray-500 mt-0.5">{{ filters.make.length }} selected</p>
                  </div>
                </div>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 transition-transform" :class="{ 'rotate-180': activePopover === 'make' }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              <!-- Make Popover -->
              <div v-if="activePopover === 'make'" class="absolute z-30 left-0 right-0 mt-2 bg-white rounded-lg shadow-lg p-4 border border-gray-200 transition-all duration-300 max-h-[300px] overflow-y-auto">
                <div class="space-y-2">
                  <div
                    v-for="make in makeOptions"
                    :key="make"
                    class="flex items-center p-2 hover:bg-gray-50 rounded-md transition-colors"
                  >
                    <div class="relative flex items-center">
                      <input
                        type="checkbox"
                        :id="`make-${make}`"
                        :checked="filters.make.includes(make)"
                        @change="toggleFilter('make', make)"
                        class="w-4 h-4 text-secondary border-gray-300 rounded focus:ring-secondary"
                      />
                      <label :for="`make-${make}`" class="ml-2 text-sm font-medium text-gray-700">{{ make }}</label>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Model Filter (dependent on make selection) -->
            <div class="mb-6 relative">
              <button
                @click="togglePopover('model')"
                class="w-full flex items-center justify-between bg-gray-50 hover:bg-gray-100 transition-colors rounded-lg p-3 text-left"
                :class="{
                  'bg-gray-100 ring-2 ring-secondary/20 relative z-30': activePopover === 'model',
                  'opacity-50 cursor-not-allowed': !isMakeSelected
                }"
                ref="filterRefs['model']"
                :disabled="!isMakeSelected"
              >
                <div class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                  <div>
                    <span class="font-medium">Model</span>
                    <p v-if="!isMakeSelected" class="text-xs text-gray-500 mt-0.5">Select a make first</p>
                    <p v-else-if="filters.model.length" class="text-xs text-gray-500 mt-0.5">{{ filters.model.length }} selected</p>
                  </div>
                </div>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 transition-transform" :class="{ 'rotate-180': activePopover === 'model' }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              <!-- Model Popover -->
              <div v-if="activePopover === 'model' && isMakeSelected" class="absolute z-30 left-0 right-0 mt-2 bg-white rounded-lg shadow-lg p-4 border border-gray-200 transition-all duration-300 max-h-[300px] overflow-y-auto">
                <div class="space-y-2">
                  <div
                    v-for="model in modelOptions"
                    :key="model"
                    class="flex items-center p-2 hover:bg-gray-50 rounded-md transition-colors"
                  >
                    <div class="relative flex items-center">
                      <input
                        type="checkbox"
                        :id="`model-${model}`"
                        :checked="filters.model.includes(model)"
                        @change="toggleFilter('model', model)"
                        class="w-4 h-4 text-secondary border-gray-300 rounded focus:ring-secondary"
                      />
                      <label :for="`model-${model}`" class="ml-2 text-sm font-medium text-gray-700">{{ model }}</label>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Color Filter -->
            <div class="mb-6 relative">
              <button
                @click="togglePopover('color')"
                class="w-full flex items-center justify-between bg-gray-50 hover:bg-gray-100 transition-colors rounded-lg p-3 text-left"
                :class="{ 'bg-gray-100 ring-2 ring-secondary/20 relative z-30': activePopover === 'color' }"
                ref="filterRefs['color']"
              >
                <div class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
                  </svg>
                  <div>
                    <span class="font-medium">Color</span>
                    <p v-if="filters.color.length" class="text-xs text-gray-500 mt-0.5">{{ filters.color.length }} selected</p>
                  </div>
                </div>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 transition-transform" :class="{ 'rotate-180': activePopover === 'color' }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              <!-- Color Popover -->
              <div v-if="activePopover === 'color'" class="absolute z-30 left-0 right-0 mt-2 bg-white rounded-lg shadow-lg p-4 border border-gray-200 transition-all duration-300 max-h-[300px] overflow-y-auto">
                <div class="space-y-2">
                  <div
                    v-for="color in colorOptions"
                    :key="color"
                    class="flex items-center p-2 hover:bg-gray-50 rounded-md transition-colors"
                  >
                    <div class="relative flex items-center">
                      <input
                        type="checkbox"
                        :id="`color-${color}`"
                        :checked="filters.color.includes(color)"
                        @change="toggleFilter('color', color)"
                        class="w-4 h-4 text-secondary border-gray-300 rounded focus:ring-secondary"
                      />
                      <label :for="`color-${color}`" class="ml-2 text-sm font-medium text-gray-700">{{ color }}</label>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Body Type Filter -->
            <div class="mb-6 relative">
              <button
                @click="togglePopover('bodyType')"
                class="w-full flex items-center justify-between bg-gray-50 hover:bg-gray-100 transition-colors rounded-lg p-3 text-left"
                :class="{ 'bg-gray-100 ring-2 ring-secondary/20 relative z-30': activePopover === 'bodyType' }"
                ref="filterRefs['bodyType']"
              >
                <div class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                  <div>
                    <span class="font-medium">Body Type</span>
                    <p v-if="filters.bodyType.length" class="text-xs text-gray-500 mt-0.5">{{ filters.bodyType.length }} selected</p>
                  </div>
                </div>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 transition-transform" :class="{ 'rotate-180': activePopover === 'bodyType' }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              <!-- Body Type Popover -->
              <div v-if="activePopover === 'bodyType'" class="absolute z-30 left-0 right-0 mt-2 bg-white rounded-lg shadow-lg p-4 border border-gray-200 transition-all duration-300 max-h-[300px] overflow-y-auto">
                <div class="grid grid-cols-2 gap-2">
                  <div
                    v-for="type in bodyTypeOptions"
                    :key="type"
                    class="flex items-center p-2 hover:bg-gray-50 rounded-md transition-colors"
                  >
                    <div class="relative flex items-center">
                      <input
                        type="checkbox"
                        :id="`body-${type}`"
                        :checked="filters.bodyType.includes(type)"
                        @change="toggleFilter('bodyType', type)"
                        class="w-4 h-4 text-secondary border-gray-300 rounded focus:ring-secondary"
                      />
                      <label :for="`body-${type}`" class="ml-2 text-sm font-medium text-gray-700">{{ type }}</label>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Transmission Filter -->
            <div class="mb-6 relative">
              <button
                @click="togglePopover('transmission')"
                class="w-full flex items-center justify-between bg-gray-50 hover:bg-gray-100 transition-colors rounded-lg p-3 text-left"
                :class="{ 'bg-gray-100 ring-2 ring-secondary/20 relative z-30': activePopover === 'transmission' }"
                ref="filterRefs['transmission']"
              >
                <div class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  <div>
                    <span class="font-medium">Transmission</span>
                    <p v-if="filters.transmission.length" class="text-xs text-gray-500 mt-0.5">{{ filters.transmission.length }} selected</p>
                  </div>
                </div>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 transition-transform" :class="{ 'rotate-180': activePopover === 'transmission' }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              <!-- Transmission Popover -->
              <div v-if="activePopover === 'transmission'" class="absolute z-30 left-0 right-0 mt-2 bg-white rounded-lg shadow-lg p-4 border border-gray-200 transition-all duration-300 max-h-[300px] overflow-y-auto">
                <div class="space-y-2">
                  <div
                    v-for="transmission in transmissionOptions"
                    :key="transmission"
                    class="flex items-center p-2 hover:bg-gray-50 rounded-md transition-colors"
                  >
                    <div class="relative flex items-center">
                      <input
                        type="checkbox"
                        :id="`transmission-${transmission}`"
                        :checked="filters.transmission.includes(transmission)"
                        @change="toggleFilter('transmission', transmission)"
                        class="w-4 h-4 text-secondary border-gray-300 rounded focus:ring-secondary"
                      />
                      <label :for="`transmission-${transmission}`" class="ml-2 text-sm font-medium text-gray-700">{{ transmission }}</label>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Fuel Type Filter -->
            <div class="mb-6 relative">
              <button
                @click="togglePopover('fuelType')"
                class="w-full flex items-center justify-between bg-gray-50 hover:bg-gray-100 transition-colors rounded-lg p-3 text-left"
                :class="{ 'bg-gray-100 ring-2 ring-secondary/20 relative z-30': activePopover === 'fuelType' }"
                ref="filterRefs['fuelType']"
              >
                <div class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                  <div>
                    <span class="font-medium">Fuel Type</span>
                    <p v-if="filters.fuelType.length" class="text-xs text-gray-500 mt-0.5">{{ filters.fuelType.length }} selected</p>
                  </div>
                </div>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 transition-transform" :class="{ 'rotate-180': activePopover === 'fuelType' }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              <!-- Fuel Type Popover -->
              <div v-if="activePopover === 'fuelType'" class="absolute z-30 left-0 right-0 mt-2 bg-white rounded-lg shadow-lg p-4 border border-gray-200 transition-all duration-300 max-h-[300px] overflow-y-auto">
                <div class="space-y-2">
                  <div
                    v-for="fuel in fuelTypeOptions"
                    :key="fuel"
                    class="flex items-center p-2 hover:bg-gray-50 rounded-md transition-colors"
                  >
                    <div class="relative flex items-center">
                      <input
                        type="checkbox"
                        :id="`fuel-${fuel}`"
                        :checked="filters.fuelType.includes(fuel)"
                        @change="toggleFilter('fuelType', fuel)"
                        class="w-4 h-4 text-secondary border-gray-300 rounded focus:ring-secondary"
                      />
                      <label :for="`fuel-${fuel}`" class="ml-2 text-sm font-medium text-gray-700">{{ fuel }}</label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Main Content -->
        <div class="flex-grow relative">
          <!-- Search and Sort Controls -->
          <!--
            Sticky scroll behavior commented out - can be restored if needed
            Original class: "sticky top-24 z-10 bg-white rounded-lg shadow-custom p-4 transition-opacity duration-300 hover:opacity-100" :class="{ 'opacity-30': isScrolled }"
          -->
          <div class="bg-white rounded-lg shadow-custom p-4 mb-6">
            <div class="flex flex-col md:flex-row md:items-center justify-between gap-4">
              <!-- Search -->
              <div class="relative flex-grow">
                <input
                  v-model="filters.search"
                  type="text"
                  placeholder="Search by make, model, year, color, body style, or any keyword..."
                  class="w-full px-4 py-2 rounded-md border border-gray-300 focus:outline-none focus:border-secondary"
                  @focus="isScrolled = false"
                />
              </div>

              <!-- Sort and Filter Controls -->
              <div class="flex items-center gap-4">
                <!-- Mobile Filter Button -->
                <button
                  @click="toggleMobileFilters(); isScrolled = false;"
                  class="lg:hidden flex items-center text-sm font-medium text-gray-700 hover:text-secondary relative"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                  </svg>
                  Filters
                  <span v-if="activeFilterCount > 0" class="absolute -top-2 -right-2 w-5 h-5 rounded-full bg-secondary text-white text-xs flex items-center justify-center">
                    {{ activeFilterCount }}
                  </span>
                </button>

                <!-- Sort Dropdown -->
                <div class="relative">
                  <select
                    v-model="sortOption"
                    class="appearance-none bg-white border border-gray-300 rounded-md py-2 pl-3 pr-10 text-sm focus:outline-none focus:border-secondary"
                    @focus="isScrolled = false"
                  >
                    <option value="newest">Newest First</option>
                    <option value="oldest">Oldest First</option>
                    <option value="price-high">Price: High to Low</option>
                    <option value="price-low">Price: Low to High</option>
                    <option value="mileage-low">Mileage: Low to High</option>
                    <option value="mileage-high">Mileage: High to Low</option>
                  </select>
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </div>
            </div>
          </div>

          <!-- Mobile Filters (Slide-in panel) -->
          <div
            v-if="showMobileFilters"
            class="fixed inset-0 z-50 lg:hidden"
          >
            <!-- Backdrop -->
            <div
              class="absolute inset-0 bg-black/50 transition-opacity duration-300"
              @click="toggleMobileFilters"
            ></div>

            <!-- Filter Panel -->
            <div class="absolute right-0 top-0 bottom-0 w-80 bg-white shadow-lg overflow-y-auto transition-all duration-300">
              <div class="p-6">
                <div class="flex items-center justify-between mb-6">
                  <h3 class="text-lg font-heading font-bold">Filters</h3>
                  <button @click="toggleMobileFilters" class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div class="flex items-center justify-between mb-6">
                  <button @click="resetFilters" class="text-secondary text-sm font-medium hover:underline flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    Reset All Filters
                  </button>

                  <span class="text-sm text-gray-500">
                    {{ filteredVehicles.length }} {{ filteredVehicles.length === 1 ? 'result' : 'results' }}
                  </span>
                </div>

                <!-- Active Filters Summary -->
                <div v-if="filters.make.length || filters.model.length || filters.color.length || filters.bodyType.length || filters.transmission.length || filters.fuelType.length"
                     class="mb-6 flex flex-wrap gap-2">
                  <div v-for="make in filters.make" :key="`mobile-active-make-${make}`"
                       class="filter-tag">
                    {{ make }}
                    <button @click="toggleFilter('make', make)" class="ml-1 text-secondary hover:text-secondary-dark">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                  <div v-for="model in filters.model" :key="`mobile-active-model-${model}`"
                       class="filter-tag">
                    {{ model }}
                    <button @click="toggleFilter('model', model)" class="ml-1 text-secondary hover:text-secondary-dark">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                  <div v-for="color in filters.color" :key="`mobile-active-color-${color}`"
                       class="filter-tag">
                    {{ color }}
                    <button @click="toggleFilter('color', color)" class="ml-1 text-secondary hover:text-secondary-dark">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                  <div v-for="type in filters.bodyType" :key="`mobile-active-body-${type}`"
                       class="filter-tag">
                    {{ type }}
                    <button @click="toggleFilter('bodyType', type)" class="ml-1 text-secondary hover:text-secondary-dark">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                  <div v-for="transmission in filters.transmission" :key="`mobile-active-transmission-${transmission}`"
                       class="filter-tag">
                    {{ transmission }}
                    <button @click="toggleFilter('transmission', transmission)" class="ml-1 text-secondary hover:text-secondary-dark">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                  <div v-for="fuel in filters.fuelType" :key="`mobile-active-fuel-${fuel}`"
                       class="filter-tag">
                    {{ fuel }}
                    <button @click="toggleFilter('fuelType', fuel)" class="ml-1 text-secondary hover:text-secondary-dark">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                </div>

                <!-- Price Range Filter -->
                <div class="mb-6">
                  <!-- TEMPORARILY DISABLED: Mobile Price Range Filter -->
                  <button
                    id="mobile-category-priceRange"
                    class="w-full flex items-center justify-between bg-gray-50 transition-colors rounded-lg p-3 text-left relative disabled-filter"
                    disabled
                  >
                    <!-- Coming soon label -->
                    <div class="absolute right-3 top-0 transform -translate-y-1/2">
                      <span class="text-xs font-medium text-red-500 bg-white px-1 rounded">Coming soon!</span>
                    </div>
                    <div class="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <div>
                        <span class="font-medium">Price Range</span>
                        <p class="text-xs text-gray-500 mt-0.5">{{ formatPrice(filters.priceRange[0]) }} - {{ formatPrice(filters.priceRange[1]) }}</p>
                      </div>
                    </div>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 transition-transform" :class="{ 'rotate-180': expandedCategories.priceRange }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>

                  <!-- Price Range Content -->
                  <div v-if="expandedCategories.priceRange" class="mt-3 p-3 bg-white rounded-lg border border-gray-200 transition-all duration-300 max-h-[300px] overflow-y-auto">
                    <div class="flex items-center justify-between mb-2">
                      <span class="text-sm font-medium text-gray-600">{{ formatPrice(filters.priceRange[0]) }}</span>
                      <span class="text-sm font-medium text-gray-600">{{ formatPrice(filters.priceRange[1]) }}</span>
                    </div>
                    <div class="relative h-14 pt-5 pb-2">
                      <!-- Track background -->
                      <div class="absolute top-6 left-0 right-0 h-2 bg-gray-200 rounded-full"></div>

                      <!-- Selected range -->
                      <div
                        class="absolute top-6 h-2 bg-gradient-to-r from-secondary/70 to-secondary rounded-full"
                        :style="{
                          left: `${getTrackPercentage(filters.priceRange[0], 0, 300000)}%`,
                          right: `${100 - getTrackPercentage(filters.priceRange[1], 0, 300000)}%`
                        }"
                      ></div>

                      <!-- Slider controls -->
                      <div class="relative mt-[-10px]">
                        <input
                          type="range"
                          v-model.number="filters.priceRange[0]"
                          min="0"
                          max="300000"
                          step="10000"
                          class="range-slider range-slider-min"
                          @input="ensurePriceRangeOrder"
                        />

                        <input
                          type="range"
                          v-model.number="filters.priceRange[1]"
                          min="0"
                          max="300000"
                          step="10000"
                          class="range-slider range-slider-max"
                          @input="ensurePriceRangeOrder"
                        />
                      </div>
                    </div>
                    <div class="grid grid-cols-2 gap-4 mt-4">
                      <div>
                        <label class="text-xs text-gray-500 mb-1 block">Min Price</label>
                        <input
                          type="number"
                          v-model.number="filters.priceRange[0]"
                          min="0"
                          max="300000"
                          step="10000"
                          class="w-full p-2 border border-gray-300 rounded text-sm"
                          @input="ensurePriceRangeOrder"
                        />
                      </div>
                      <div>
                        <label class="text-xs text-gray-500 mb-1 block">Max Price</label>
                        <input
                          type="number"
                          v-model.number="filters.priceRange[1]"
                          min="0"
                          max="300000"
                          step="10000"
                          class="w-full p-2 border border-gray-300 rounded text-sm"
                          @input="ensurePriceRangeOrder"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Kilometers Range Filter -->
                <div class="mb-6">
                  <!-- TEMPORARILY DISABLED: Mobile Kilometers Range Filter -->
                  <button
                    id="mobile-category-kilometersRange"
                    class="w-full flex items-center justify-between bg-gray-50 transition-colors rounded-lg p-3 text-left relative disabled-filter"
                    disabled
                  >
                    <!-- Coming soon label -->
                    <div class="absolute right-3 top-0 transform -translate-y-1/2">
                      <span class="text-xs font-medium text-red-500 bg-white px-1 rounded">Coming soon!</span>
                    </div>
                    <div class="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <div>
                        <span class="font-medium">Kilometers</span>
                        <p class="text-xs text-gray-500 mt-0.5">{{ filters.kilometersRange[0].toLocaleString() }} - {{ filters.kilometersRange[1].toLocaleString() }} km</p>
                      </div>
                    </div>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 transition-transform" :class="{ 'rotate-180': expandedCategories.kilometersRange }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>

                  <!-- Kilometers Range Content -->
                  <div v-if="expandedCategories.kilometersRange" class="mt-3 p-3 bg-white rounded-lg border border-gray-200 transition-all duration-300 max-h-[300px] overflow-y-auto">
                    <div class="flex items-center justify-between mb-2">
                      <span class="text-sm font-medium text-gray-600">{{ filters.kilometersRange[0].toLocaleString() }} km</span>
                      <span class="text-sm font-medium text-gray-600">{{ filters.kilometersRange[1].toLocaleString() }} km</span>
                    </div>
                    <div class="relative pt-6 pb-2">
                      <!-- Track background -->
                      <div class="absolute left-0 right-0 h-2 bg-gray-200 rounded-full"></div>

                      <!-- Selected range -->
                      <div
                        class="absolute h-2 bg-gradient-to-r from-secondary/70 to-secondary rounded-full"
                        :style="{
                          left: (filters.kilometersRange[0] / 20000 * 100) + '%',
                          right: (100 - filters.kilometersRange[1] / 20000 * 100) + '%'
                        }"
                      ></div>

                      <!-- Min handle -->
                      <input
                        type="range"
                        v-model.number="filters.kilometersRange[0]"
                        min="0"
                        max="20000"
                        step="1000"
                        class="absolute w-full appearance-none bg-transparent cursor-pointer"
                        style="height: 20px; top: 0; pointer-events: auto;"
                        @input="ensureKilometersRangeOrder"
                      />

                      <!-- Max handle -->
                      <input
                        type="range"
                        v-model.number="filters.kilometersRange[1]"
                        min="0"
                        max="20000"
                        step="1000"
                        class="absolute w-full appearance-none bg-transparent cursor-pointer"
                        style="height: 20px; top: 0; pointer-events: auto;"
                        @input="ensureKilometersRangeOrder"
                      />
                    </div>
                    <div class="grid grid-cols-2 gap-4 mt-4">
                      <div>
                        <label class="text-xs text-gray-500 mb-1 block">Min Kilometers</label>
                        <input
                          type="number"
                          v-model.number="filters.kilometersRange[0]"
                          min="0"
                          max="20000"
                          step="1000"
                          class="w-full p-2 border border-gray-300 rounded text-sm"
                          @input="ensureKilometersRangeOrder"
                        />
                      </div>
                      <div>
                        <label class="text-xs text-gray-500 mb-1 block">Max Kilometers</label>
                        <input
                          type="number"
                          v-model.number="filters.kilometersRange[1]"
                          min="0"
                          max="20000"
                          step="1000"
                          class="w-full p-2 border border-gray-300 rounded text-sm"
                          @input="ensureKilometersRangeOrder"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Year Range Filter -->
                <div class="mb-6">
                  <!-- TEMPORARILY DISABLED: Mobile Year Range Filter -->
                  <button
                    id="mobile-category-yearRange"
                    class="w-full flex items-center justify-between bg-gray-50 transition-colors rounded-lg p-3 text-left relative disabled-filter"
                    disabled
                  >
                    <!-- Coming soon label -->
                    <div class="absolute right-3 top-0 transform -translate-y-1/2">
                      <span class="text-xs font-medium text-red-500 bg-white px-1 rounded">Coming soon!</span>
                    </div>
                    <div class="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      <div>
                        <span class="font-medium">Year</span>
                        <p class="text-xs text-gray-500 mt-0.5">{{ filters.yearRange[0] }} - {{ filters.yearRange[1] }}</p>
                      </div>
                    </div>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 transition-transform" :class="{ 'rotate-180': expandedCategories.yearRange }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>

                  <!-- Year Range Content -->
                  <div v-if="expandedCategories.yearRange" class="mt-3 p-3 bg-white rounded-lg border border-gray-200 transition-all duration-300 max-h-[300px] overflow-y-auto">
                    <div class="flex items-center justify-between mb-2">
                      <span class="text-sm font-medium text-gray-600">{{ filters.yearRange[0] }}</span>
                      <span class="text-sm font-medium text-gray-600">{{ filters.yearRange[1] }}</span>
                    </div>
                    <div class="relative pt-6 pb-2">
                      <!-- Track background -->
                      <div class="absolute left-0 right-0 h-2 bg-gray-200 rounded-full"></div>

                      <!-- Selected range -->
                      <div
                        class="absolute h-2 bg-gradient-to-r from-secondary/70 to-secondary rounded-full"
                        :style="{
                          left: ((filters.yearRange[0] - 2020) / 4 * 100) + '%',
                          right: (100 - (filters.yearRange[1] - 2020) / 4 * 100) + '%'
                        }"
                      ></div>

                      <!-- Min handle -->
                      <input
                        type="range"
                        v-model.number="filters.yearRange[0]"
                        min="2020"
                        max="2024"
                        step="1"
                        class="absolute w-full appearance-none bg-transparent cursor-pointer"
                        style="height: 20px; top: 0; pointer-events: auto;"
                        @input="ensureYearRangeOrder"
                      />

                      <!-- Max handle -->
                      <input
                        type="range"
                        v-model.number="filters.yearRange[1]"
                        min="2020"
                        max="2024"
                        step="1"
                        class="absolute w-full appearance-none bg-transparent cursor-pointer"
                        style="height: 20px; top: 0; pointer-events: auto;"
                        @input="ensureYearRangeOrder"
                      />
                    </div>
                    <div class="grid grid-cols-2 gap-4 mt-4">
                      <div>
                        <label class="text-xs text-gray-500 mb-1 block">Min Year</label>
                        <input
                          type="number"
                          v-model.number="filters.yearRange[0]"
                          min="2020"
                          max="2024"
                          step="1"
                          class="w-full p-2 border border-gray-300 rounded text-sm"
                          @input="ensureYearRangeOrder"
                        />
                      </div>
                      <div>
                        <label class="text-xs text-gray-500 mb-1 block">Max Year</label>
                        <input
                          type="number"
                          v-model.number="filters.yearRange[1]"
                          min="2020"
                          max="2024"
                          step="1"
                          class="w-full p-2 border border-gray-300 rounded text-sm"
                          @input="ensureYearRangeOrder"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Make Filter -->
                <div class="mb-6">
                  <button
                    id="mobile-category-make"
                    @click="toggleCategory('make')"
                    class="w-full flex items-center justify-between bg-gray-50 hover:bg-gray-100 transition-colors rounded-lg p-3 text-left"
                    :class="{ 'bg-gray-100 ring-2 ring-secondary/20': expandedCategories.make }"
                  >
                    <div class="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                      </svg>
                      <div>
                        <span class="font-medium">Make</span>
                        <p v-if="filters.make.length" class="text-xs text-gray-500 mt-0.5">{{ filters.make.length }} selected</p>
                      </div>
                    </div>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 transition-transform" :class="{ 'rotate-180': expandedCategories.make }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>

                  <!-- Make Content -->
                  <div v-if="expandedCategories.make" class="mt-3 p-3 bg-white rounded-lg border border-gray-200 transition-all duration-300 max-h-[300px] overflow-y-auto">
                    <div class="space-y-2">
                      <div
                        v-for="make in makeOptions"
                        :key="make"
                        class="flex items-center p-2 hover:bg-gray-50 rounded-md transition-colors"
                      >
                        <div class="relative flex items-center">
                          <input
                            type="checkbox"
                            :id="`mobile-make-${make}`"
                            :checked="filters.make.includes(make)"
                            @change="toggleFilter('make', make)"
                            class="w-4 h-4 text-secondary border-gray-300 rounded focus:ring-secondary"
                          />
                          <label :for="`mobile-make-${make}`" class="ml-2 text-sm font-medium text-gray-700">{{ make }}</label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Model Filter -->
                <div class="mb-6">
                  <button
                    id="mobile-category-model"
                    @click="toggleCategory('model')"
                    class="w-full flex items-center justify-between bg-gray-50 hover:bg-gray-100 transition-colors rounded-lg p-3 text-left"
                    :class="{
                      'bg-gray-100 ring-2 ring-secondary/20': expandedCategories.model,
                      'opacity-50 cursor-not-allowed': !isMakeSelected
                    }"
                    :disabled="!isMakeSelected"
                  >
                    <div class="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                      </svg>
                      <div>
                        <span class="font-medium">Model</span>
                        <p v-if="!isMakeSelected" class="text-xs text-gray-500 mt-0.5">Select a make first</p>
                        <p v-else-if="filters.model.length" class="text-xs text-gray-500 mt-0.5">{{ filters.model.length }} selected</p>
                      </div>
                    </div>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 transition-transform" :class="{ 'rotate-180': expandedCategories.model }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>

                  <!-- Model Content -->
                  <div v-if="expandedCategories.model && isMakeSelected" class="mt-3 p-3 bg-white rounded-lg border border-gray-200 transition-all duration-300 max-h-[300px] overflow-y-auto">
                    <div class="space-y-2">
                      <div
                        v-for="model in modelOptions"
                        :key="model"
                        class="flex items-center p-2 hover:bg-gray-50 rounded-md transition-colors"
                      >
                        <div class="relative flex items-center">
                          <input
                            type="checkbox"
                            :id="`mobile-model-${model}`"
                            :checked="filters.model.includes(model)"
                            @change="toggleFilter('model', model)"
                            class="w-4 h-4 text-secondary border-gray-300 rounded focus:ring-secondary"
                          />
                          <label :for="`mobile-model-${model}`" class="ml-2 text-sm font-medium text-gray-700">{{ model }}</label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Color Filter -->
                <div class="mb-6">
                  <button
                    id="mobile-category-color"
                    @click="toggleCategory('color')"
                    class="w-full flex items-center justify-between bg-gray-50 hover:bg-gray-100 transition-colors rounded-lg p-3 text-left"
                    :class="{ 'bg-gray-100 ring-2 ring-secondary/20': expandedCategories.color }"
                  >
                    <div class="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
                      </svg>
                      <div>
                        <span class="font-medium">Color</span>
                        <p v-if="filters.color.length" class="text-xs text-gray-500 mt-0.5">{{ filters.color.length }} selected</p>
                      </div>
                    </div>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 transition-transform" :class="{ 'rotate-180': expandedCategories.color }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>

                  <!-- Color Content -->
                  <div v-if="expandedCategories.color" class="mt-3 p-3 bg-white rounded-lg border border-gray-200 transition-all duration-300 max-h-[300px] overflow-y-auto">
                    <div class="space-y-2">
                      <div
                        v-for="color in colorOptions"
                        :key="color"
                        class="flex items-center p-2 hover:bg-gray-50 rounded-md transition-colors"
                      >
                        <div class="relative flex items-center">
                          <input
                            type="checkbox"
                            :id="`mobile-color-${color}`"
                            :checked="filters.color.includes(color)"
                            @change="toggleFilter('color', color)"
                            class="w-4 h-4 text-secondary border-gray-300 rounded focus:ring-secondary"
                          />
                          <label :for="`mobile-color-${color}`" class="ml-2 text-sm font-medium text-gray-700">{{ color }}</label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Body Type Filter -->
                <div class="mb-6">
                  <button
                    id="mobile-category-bodyType"
                    @click="toggleCategory('bodyType')"
                    class="w-full flex items-center justify-between bg-gray-50 hover:bg-gray-100 transition-colors rounded-lg p-3 text-left"
                    :class="{ 'bg-gray-100 ring-2 ring-secondary/20': expandedCategories.bodyType }"
                  >
                    <div class="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                      </svg>
                      <div>
                        <span class="font-medium">Body Type</span>
                        <p v-if="filters.bodyType.length" class="text-xs text-gray-500 mt-0.5">{{ filters.bodyType.length }} selected</p>
                      </div>
                    </div>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 transition-transform" :class="{ 'rotate-180': expandedCategories.bodyType }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>

                  <!-- Body Type Content -->
                  <div v-if="expandedCategories.bodyType" class="mt-3 p-3 bg-white rounded-lg border border-gray-200 transition-all duration-300 max-h-[300px] overflow-y-auto">
                    <div class="grid grid-cols-2 gap-2">
                      <div
                        v-for="type in bodyTypeOptions"
                        :key="type"
                        class="flex items-center p-2 hover:bg-gray-50 rounded-md transition-colors"
                      >
                        <div class="relative flex items-center">
                          <input
                            type="checkbox"
                            :id="`mobile-body-${type}`"
                            :checked="filters.bodyType.includes(type)"
                            @change="toggleFilter('bodyType', type)"
                            class="w-4 h-4 text-secondary border-gray-300 rounded focus:ring-secondary"
                          />
                          <label :for="`mobile-body-${type}`" class="ml-2 text-sm font-medium text-gray-700">{{ type }}</label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Transmission Filter -->
                <div class="mb-6">
                  <button
                    id="mobile-category-transmission"
                    @click="toggleCategory('transmission')"
                    class="w-full flex items-center justify-between bg-gray-50 hover:bg-gray-100 transition-colors rounded-lg p-3 text-left"
                    :class="{ 'bg-gray-100 ring-2 ring-secondary/20': expandedCategories.transmission }"
                  >
                    <div class="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                      <div>
                        <span class="font-medium">Transmission</span>
                        <p v-if="filters.transmission.length" class="text-xs text-gray-500 mt-0.5">{{ filters.transmission.length }} selected</p>
                      </div>
                    </div>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 transition-transform" :class="{ 'rotate-180': expandedCategories.transmission }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>

                  <!-- Transmission Content -->
                  <div v-if="expandedCategories.transmission" class="mt-3 p-3 bg-white rounded-lg border border-gray-200 transition-all duration-300 max-h-[300px] overflow-y-auto">
                    <div class="space-y-2">
                      <div
                        v-for="transmission in transmissionOptions"
                        :key="transmission"
                        class="flex items-center p-2 hover:bg-gray-50 rounded-md transition-colors"
                      >
                        <div class="relative flex items-center">
                          <input
                            type="checkbox"
                            :id="`mobile-transmission-${transmission}`"
                            :checked="filters.transmission.includes(transmission)"
                            @change="toggleFilter('transmission', transmission)"
                            class="w-4 h-4 text-secondary border-gray-300 rounded focus:ring-secondary"
                          />
                          <label :for="`mobile-transmission-${transmission}`" class="ml-2 text-sm font-medium text-gray-700">{{ transmission }}</label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Fuel Type Filter -->
                <div class="mb-6">
                  <button
                    id="mobile-category-fuelType"
                    @click="toggleCategory('fuelType')"
                    class="w-full flex items-center justify-between bg-gray-50 hover:bg-gray-100 transition-colors rounded-lg p-3 text-left"
                    :class="{ 'bg-gray-100 ring-2 ring-secondary/20': expandedCategories.fuelType }"
                  >
                    <div class="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                      <div>
                        <span class="font-medium">Fuel Type</span>
                        <p v-if="filters.fuelType.length" class="text-xs text-gray-500 mt-0.5">{{ filters.fuelType.length }} selected</p>
                      </div>
                    </div>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 transition-transform" :class="{ 'rotate-180': expandedCategories.fuelType }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>

                  <!-- Fuel Type Content -->
                  <div v-if="expandedCategories.fuelType" class="mt-3 p-3 bg-white rounded-lg border border-gray-200 transition-all duration-300 max-h-[300px] overflow-y-auto">
                    <div class="space-y-2">
                      <div
                        v-for="fuel in fuelTypeOptions"
                        :key="fuel"
                        class="flex items-center p-2 hover:bg-gray-50 rounded-md transition-colors"
                      >
                        <div class="relative flex items-center">
                          <input
                            type="checkbox"
                            :id="`mobile-fuel-${fuel}`"
                            :checked="filters.fuelType.includes(fuel)"
                            @change="toggleFilter('fuelType', fuel)"
                            class="w-4 h-4 text-secondary border-gray-300 rounded focus:ring-secondary"
                          />
                          <label :for="`mobile-fuel-${fuel}`" class="ml-2 text-sm font-medium text-gray-700">{{ fuel }}</label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <button
                  @click="toggleMobileFilters"
                  class="w-full btn btn-primary"
                >
                  Apply Filters
                </button>
              </div>
            </div>
          </div>

          <!-- Results Count -->
          <div class="mb-6 mt-4">
            <p class="text-gray-600">
              Showing {{ filteredVehicles.length }} {{ filteredVehicles.length === 1 ? 'vehicle' : 'vehicles' }}
            </p>
          </div>

          <!-- Vehicle Grid - Always show if we have vehicles and they're ready -->
          <div v-if="itemsReady && filteredVehicles.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            <div
              v-for="(vehicle, index) in filteredVehicles"
              :key="vehicle.id"
              class="card-custom group fade-in-card"
              :style="{ animationDelay: `${Math.min(Math.floor(index / 20) * 0.1 + (index % 20) * 0.02, 1)}s` }"
            >
              <!-- Image Container -->
              <router-link :to="`/vehicle/${vehicle.id}`" class="relative overflow-hidden h-56 md:h-64 rounded-t-lg bg-gray-100 block cursor-pointer">
                <img
                  :src="vehicle.image || '/images/no-image-available.jpg'"
                  :alt="extractDetails(vehicle).mainTitle"
                  class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                  loading="lazy"
                  decoding="async"
                  fetchpriority="low"
                  onerror="this.onerror=null; this.src='/images/no-image-available.jpg'; this.classList.add('error-image');"
                  :class="{ 'loaded': vehicle.image && vehicle.image !== '/images/no-image-available.jpg' }"
                  :style="{ opacity: '1', transition: 'opacity 0.3s ease-in' }"
                  @error="$event.target.src='/images/no-image-available.jpg'; $event.target.classList.add('error-image');"
                  @load="$event.target.classList.add('loaded')"
                />
                <!-- Price Overlay -->
                <span class="absolute bottom-0 right-0 bg-secondary text-white px-3 py-1 rounded-tl-lg text-sm font-semibold shadow-md z-10">
                  {{ formatPrice(vehicle.price) }}
                </span>
                <!-- Subtle gradient overlay for better text visibility -->
                <div class="absolute inset-x-0 bottom-0 h-16 bg-gradient-to-t from-black/50 to-transparent opacity-70 group-hover:opacity-80 transition-opacity duration-300"></div>
              </router-link>

              <!-- Content Container -->
              <div class="p-4 md:p-5 flex flex-col h-64">
                <!-- Title and Subtitle -->
                <div class="mb-3">
                  <router-link :to="`/vehicle/${vehicle.id}`">
                    <h3 class="text-lg font-semibold text-gray-800 leading-tight truncate group-hover:text-secondary transition-colors cursor-pointer" :title="extractDetails(vehicle).mainTitle">
                      {{ extractDetails(vehicle).mainTitle }}
                    </h3>
                  </router-link>
                  <p v-if="extractDetails(vehicle).subtitle" class="text-sm text-gray-500 font-medium mt-1 truncate" :title="extractDetails(vehicle).subtitle">
                    {{ extractDetails(vehicle).subtitle }}
                  </p>
                </div>

                <!-- Details Grid -->
                <div class="grid grid-cols-2 gap-x-4 gap-y-2 mb-4 text-sm text-gray-600">
                  <div class="flex items-center" title="Mileage">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5 text-gray-400 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span class="truncate">{{ vehicle.mileage ? formatNumber(vehicle.mileage) + ' km' : 'N/A' }}</span>
                  </div>
                  <div class="flex items-center" title="Year">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5 text-gray-400 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <span class="truncate">{{ vehicle.year || 'N/A' }}</span>
                  </div>
                  <div class="flex items-center" title="Transmission">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5 text-gray-400 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                      <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <span class="truncate">{{ vehicle.transmission || 'N/A' }}</span>
                  </div>
                  <div class="flex items-center" title="Fuel Type">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5 text-gray-400 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                    <span class="truncate">{{ vehicle.fuelType || 'N/A' }}</span>
                  </div>
                </div>

                <!-- Description (if available) -->
                <p v-if="vehicle.description" class="text-sm text-gray-600 mb-4 line-clamp-2">
                  {{ vehicle.description }}
                </p>

                <!-- View Details Button -->
                <div class="mt-auto">
                  <router-link :to="`/vehicle/${vehicle.id}`" class="btn btn-primary w-full text-center mb-2 hover:scale-105 transition-transform">
                    View Details
                  </router-link>
                  <div class="text-xs text-gray-500 mt-1 text-center">
                    ID: {{ vehicle.id }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Loading More Indicator - shown at the bottom when loading more vehicles -->
          <div v-if="loadingMore || (vehicleStore.isLoading.value && currentPage > 1)" class="flex justify-center items-center py-8 mt-4">
            <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-secondary"></div>
            <span class="ml-3 text-gray-600 font-medium">Loading more vehicles...</span>
          </div>

          <!-- Error state with retry button -->
          <div v-else-if="loadMoreError && vehicleStore.hasMoreVehicles.value" class="flex flex-col justify-center items-center py-8 mt-4 bg-red-50 rounded-lg border border-red-100 px-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-red-500 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            <p class="text-red-700 font-medium mb-3">There was an error loading more vehicles</p>
            <button @click="retryLoadMore" class="btn bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md transition-colors">
              Try Again
            </button>
          </div>

          <!-- Multiple trigger elements for loading more vehicles - positioned for early loading without footer overlap -->
          <div class="relative">
            <!-- Primary trigger with higher positioning but avoiding footer overlap -->
            <div
              ref="loadMoreTrigger"
              class="h-20 w-full my-8 bg-transparent"
              style="min-height: 100px; margin-top: -400px;"
            >
              <!-- This element is positioned higher to trigger loading earlier -->
            </div>

            <!-- Multiple secondary triggers at different positions for maximum redundancy -->
            <div class="h-10 w-full absolute bottom-0" style="transform: translateY(100px);"></div>
            <div class="h-10 w-full absolute bottom-0" style="transform: translateY(300px);"></div>
            <div class="h-10 w-full absolute bottom-0" style="transform: translateY(500px);"></div>
            <div class="h-10 w-full absolute bottom-0" style="transform: translateY(800px);"></div>
          </div>
          
          <!-- Balanced padding to prevent footer overlap -->
          <div class="h-40 w-full"></div>

          <!-- Initial Loading Skeleton - only show for first page load -->
          <div v-if="(isLoading || !itemsReady) && currentPage === 1" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            <div v-for="i in 8" :key="i" class="card-custom animate-pulse">
              <!-- Image Skeleton with shimmer effect -->
              <div class="relative overflow-hidden h-56 md:h-64 rounded-t-lg bg-gray-200">
                <div class="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 shimmer-effect"></div>
              </div>

              <!-- Content Skeleton -->
              <div class="p-4 md:p-5 flex flex-col h-64">
                <!-- Title Skeleton -->
                <div class="mb-3">
                  <div class="h-6 bg-gray-200 rounded w-3/4 mb-2 shimmer-bg"></div>
                  <div class="h-4 bg-gray-200 rounded w-1/2 shimmer-bg"></div>
                </div>

                <!-- Details Skeleton -->
                <div class="grid grid-cols-2 gap-x-4 gap-y-2 mb-4">
                  <div class="flex items-center">
                    <div class="w-4 h-4 mr-1.5 bg-gray-200 rounded-full shimmer-bg"></div>
                    <div class="h-4 bg-gray-200 rounded w-20 shimmer-bg"></div>
                  </div>
                  <div class="flex items-center">
                    <div class="w-4 h-4 mr-1.5 bg-gray-200 rounded-full shimmer-bg"></div>
                    <div class="h-4 bg-gray-200 rounded w-16 shimmer-bg"></div>
                  </div>
                  <div class="flex items-center">
                    <div class="w-4 h-4 mr-1.5 bg-gray-200 rounded-full"></div>
                    <div class="h-4 bg-gray-200 rounded w-24"></div>
                  </div>
                  <div class="flex items-center">
                    <div class="w-4 h-4 mr-1.5 bg-gray-200 rounded-full"></div>
                    <div class="h-4 bg-gray-200 rounded w-20"></div>
                  </div>
                </div>

                <!-- Description Skeleton -->
                <div class="h-10 bg-gray-200 rounded mb-4"></div>

                <!-- Button Skeleton -->
                <div class="mt-auto">
                  <div class="h-10 bg-gray-300 rounded w-full mb-2"></div>
                  <div class="h-3 bg-gray-200 rounded w-1/3 mx-auto"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- End of Results Message - with improved styling and balanced padding -->
          <div v-if="!isLoading && !loadingMore && !vehicleStore.hasMoreVehicles.value && filteredVehicles.length > 0" class="w-full flex flex-col items-center justify-center py-8 mt-8 mb-32 bg-white rounded-lg shadow-sm">
            <div class="text-center max-w-md mx-auto px-6 py-4">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-secondary mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              
              <p v-if="filteredVehicles.length === 1" class="text-xl font-medium text-gray-800 mb-3">
                Showing the only vehicle that matches your filters
              </p>
              <p v-else-if="filteredVehicles.length < 5" class="text-xl font-medium text-gray-800 mb-3">
                Showing all {{ filteredVehicles.length }} vehicles that match your filters
              </p>
              <p v-else class="text-xl font-medium text-gray-800 mb-3">
                You've reached the end of the results
              </p>
              
              <button
                v-if="Object.values(filters).some(f => Array.isArray(f) ? f.length > 0 : f)"
                @click="resetFilters"
                class="mt-4 px-6 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors font-medium"
              >
                Clear Filters
              </button>
            </div>
          </div>
          
          <!-- No Vehicles Found Message - with improved styling and balanced padding -->
          <div v-if="!isLoading && !loadingMore && filteredVehicles.length === 0" class="w-full flex flex-col items-center justify-center py-12 mt-8 mb-32 bg-white rounded-lg shadow-sm">
            <div class="text-center max-w-md mx-auto px-6">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-14 w-14 text-gray-400 mx-auto mb-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
              <p class="text-2xl font-medium text-gray-800 mb-3">
                No vehicles match your current filters
              </p>
              <p class="text-gray-600 mb-8 text-lg">
                Try adjusting your filters to see more results
              </p>
              <button
                @click="resetFilters"
                class="px-8 py-3 bg-secondary text-white rounded-md hover:bg-secondary-dark transition-colors font-medium text-lg"
              >
                Reset All Filters
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<style>
/* Base slider styles */
.slider-track {
  height: 2px;
  border-radius: 9999px;
  background-color: #e5e7eb;
}

/* Custom slider handle styling */
.slider-handle {
  position: absolute;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: white;
  border: 2px solid var(--color-secondary, #3b82f6);
  box-shadow: 0 1px 3px rgba(0,0,0,0.2);
  transform: translate(-50%, -50%);
  z-index: 10;
  cursor: pointer;
  transition: transform 0.1s ease-in-out, box-shadow 0.1s ease-in-out;
}

.slider-handle:hover, .slider-handle:active {
  transform: translate(-50%, -50%) scale(1.1);
  box-shadow: 0 2px 5px rgba(0,0,0,0.3);
}

/* Active filters tag styling */
.filter-tag {
  display: flex;
  align-items: center;
  background-color: #f3f4f6;
  color: #374151;
  font-size: 0.75rem;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  transition: all 0.2s ease;
}

.filter-tag:hover {
  background-color: #e5e7eb;
}

/* Range slider styles */
.range-slider {
  -webkit-appearance: none;
  appearance: none;
  background: transparent !important; /* Force transparent background */
  width: 100%;
  height: 20px;
  position: absolute;
  top: 0;
  left: 0;
  margin: 0;
  z-index: 10;
  pointer-events: none;
  outline: none !important; /* Remove focus outline */
}

/* Remove focus styles for all browsers */
.range-slider:focus {
  outline: none !important;
  box-shadow: none !important;
  border: none !important;
}

/* Override global range input styles */
.range-slider::-webkit-slider-runnable-track {
  -webkit-appearance: none;
  appearance: none;
  background: transparent !important; /* Force transparent track */
  height: 0;
  border: none;
}

.range-slider::-moz-range-track {
  background: transparent !important; /* Force transparent track */
  height: 0;
  border: none;
}

/* Enable pointer events for thumbs */
.range-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: white;
  border: 2px solid var(--color-secondary, #3b82f6);
  box-shadow: 0 1px 3px rgba(0,0,0,0.2);
  cursor: pointer;
  pointer-events: auto;
  transition: transform 0.1s ease-in-out, box-shadow 0.1s ease-in-out;
  z-index: 20; /* Ensure thumb is above track */
  margin-top: 0; /* Reset any margin */
}

.range-slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background-color: white;
  border: 2px solid var(--color-secondary, #3b82f6);
  box-shadow: 0 1px 3px rgba(0,0,0,0.2);
  cursor: pointer;
  pointer-events: auto;
  transition: transform 0.1s ease-in-out, box-shadow 0.1s ease-in-out;
  z-index: 20; /* Ensure thumb is above track */
}

/* Hover and active states */
.range-slider::-webkit-slider-thumb:hover,
.range-slider::-webkit-slider-thumb:active {
  transform: scale(1.1);
  box-shadow: 0 2px 5px rgba(0,0,0,0.3);
}

.range-slider::-moz-range-thumb:hover,
.range-slider::-moz-range-thumb:active {
  transform: scale(1.1);
  box-shadow: 0 2px 5px rgba(0,0,0,0.3);
}

/* Z-index for proper layering */
.range-slider-min {
  z-index: 11;
}

.range-slider-max {
  z-index: 12;
}

/* Additional focus state overrides for all browsers */
.range-slider:focus-visible,
.range-slider:active,
.range-slider:focus-within {
  outline: none !important;
  box-shadow: none !important;
  border: none !important;
}

/* Remove any browser-specific focus styles */
.range-slider::-moz-focus-outer {
  border: 0;
}

/* Remove any browser-specific highlight */
.range-slider::selection {
  background: transparent;
}

/* Prevent black rectangle on click/selection */
.range-slider::-webkit-slider-thumb::selection,
.range-slider::-moz-range-thumb::selection {
  background: transparent;
}

/* Additional style to prevent selection rectangle */
.range-slider-container {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Ensure the slider thumbs don't show any selection artifacts */
.range-slider-container * {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Ensure popover doesn't shift layout */
.popover-container {
  position: relative;
}

.popover-content {
  position: absolute;
  z-index: 50;
  min-width: 100%;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border-radius: 0.5rem;
  overflow: hidden;
}

/* Transitions for smoother UX */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

.slide-right-enter-active, .slide-right-leave-active {
  transition: transform 0.3s ease;
}

.slide-right-enter-from, .slide-right-leave-to {
  transform: translateX(100%);
}

/* Loading Container Styles */
.loading-container {
  width: 100%;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
}
.card-custom {
  background-color: white;
  border-radius: 0.5rem; /* 8px */
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  transition: all 0.3s ease-in-out;
  overflow: hidden; /* Ensures content respects rounded corners */
  display: flex;
  flex-direction: column;
  height: 100%;
  will-change: transform, box-shadow;
}

.card-custom:hover {
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  transform: translateY(-4px);
}

.card-custom > div:last-child {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.mt-auto {
  margin-top: auto;
}


/* Fade-in animation for cards */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out both;
  opacity: 0;
  will-change: opacity, transform;
}

/* Fade-in animation for cards */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-card {
  animation: fadeInUp 0.4s ease-out forwards;
  will-change: opacity, transform;
  animation-fill-mode: both;
  backface-visibility: hidden;
  perspective: 1000px;
  transform-style: preserve-3d;
}

/* Pulse animation for skeleton loading */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Style for error images */
.error-image {
  object-fit: contain !important;
  padding: 1rem;
  background-color: #f3f4f6;
}

/* Image loading animation - prevent double animation */
img[loading="lazy"] {
  opacity: 0;
  transition: opacity 0.3s ease-in;
}

img[loading="lazy"].loaded {
  opacity: 1;
}

/* Prevent re-animation of already loaded images */
img.loaded {
  opacity: 1 !important;
  animation: none !important;
}

/* Filter popover animations */
.absolute[v-if] {
  animation: popoverFadeIn 0.2s ease-out forwards;
}

@keyframes popoverFadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Overlay fade effect */
.bg-white\/70 {
  animation: overlayFadeIn 0.2s ease-out forwards;
}

@keyframes overlayFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* TEMPORARY STYLES - For disabled filters */
.disabled-filter {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
  position: relative;
  overflow: visible;
}

/* Make sure the "Coming soon!" label is fully visible */
.disabled-filter .absolute {
  opacity: 1;
  z-index: 20;
}

.disabled-filter .text-red-500 {
  opacity: 1;
  font-weight: 600;
}

.disabled-filter::after {
  content: '';
  position: absolute;
  inset: 0;
  background-color: rgba(243, 244, 246, 0.5);
  border-radius: 0.5rem;
}
</style>